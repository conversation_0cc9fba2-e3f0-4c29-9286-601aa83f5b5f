#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正商圈分类 - 处理未知商圈和其他商圈问题
基于昆山当地实际地理位置进行精确分类
"""

import pandas as pd
import re
from kunshan_location_db import get_kunshan_location_database, get_special_keywords, get_road_patterns

print("=== 昆山奶茶店商圈分类修正工具 ===")
print("正在加载昆山地理位置数据库...")
print("正在修正未知商圈和其他商圈的分类...")

def load_data():
    """加载清洗后的数据"""
    try:
        df = pd.read_excel('昆山奶茶店_清洗后数据.xlsx')
        print(f"✓ 成功加载数据: {df.shape[0]} 行")
        return df
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def analyze_problem_areas(df):
    """分析问题商圈"""
    print(f"\n=== 当前商圈分布分析 ===")
    area_counts = df['商圈'].value_counts()
    
    problem_areas = ['未知商圈', '其他商圈']
    problem_count = 0
    
    for area, count in area_counts.items():
        status = "❌" if area in problem_areas else "✅"
        print(f"{status} {area}: {count}家")
        if area in problem_areas:
            problem_count += count
    
    print(f"\n需要重新分类的店铺: {problem_count}家")
    return problem_count

def create_enhanced_area_mapping():
    """创建增强的昆山商圈地址映射"""
    
    # 基于昆山实际地理位置的详细映射
    area_mapping = {
        # 玉山镇（市中心区域）
        '玉山商圈': [
            '玉山', '玉山镇', '人民路', '前进路', '同心路', '柏庐路', '亭林路', 
            '震川路', '马鞍山路', '娄江路', '玉山南路', '玉山北路', '中山路',
            '人民广场', '玉山广场', '新客站', '昆山站', '火车站', '客运站',
            '市政府', '市中心', '中心', '城中', '老城区', '市区'
        ],
        
        # 朝阳商圈
        '朝阳商圈': [
            '朝阳', '朝阳路', '朝阳东路', '朝阳西路', '朝阳中路', '朝阳门',
            '朝阳街', '朝阳新村', '朝阳花园', '朝阳小区'
        ],
        
        # 花桥商圈
        '花桥商圈': [
            '花桥', '花桥镇', '绿地大道', '花安路', '徐公桥路', '花溪路',
            '花桥国际商务城', '花桥地铁站', '兆丰路', '横泾路', '集善路',
            '花桥中心', '花桥新城', '花桥经济开发区'
        ],
        
        # 陆家商圈
        '陆家商圈': [
            '陆家', '陆家镇', '陆丰路', '菉溪路', '陆家浜路', '金阳路',
            '陆家中心', '陆家新区', '陆家老街'
        ],
        
        # 周市商圈
        '周市商圈': [
            '周市', '周市镇', '白塘路', '横长泾路', '金浦路', '周市中心',
            '周市新镇', '周市老街', '青阳路', '市北路'
        ],
        
        # 千灯商圈
        '千灯商圈': [
            '千灯', '千灯镇', '石浦路', '昆山大道', '千灯中心', '千灯古镇',
            '千灯新区', '炎武路', '淞南路'
        ],
        
        # 巴城商圈
        '巴城商圈': [
            '巴城', '巴城镇', '湖滨路', '正仪路', '巴城中心', '阳澄湖',
            '巴城老街', '湖滨新村', '巴解路'
        ],
        
        # 张浦商圈
        '张浦商圈': [
            '张浦', '张浦镇', '振新路', '港浦路', '张浦中心', '张浦新区',
            '振兴路', '浦东路', '张浦老街'
        ],
        
        # 锦溪商圈
        '锦溪商圈': [
            '锦溪', '锦溪镇', '邵甸港路', '锦溪古镇', '锦溪中心',
            '锦东路', '锦西路', '古镇路'
        ],
        
        # 淀山湖商圈
        '淀山湖商圈': [
            '淀山湖', '淀山湖镇', '淀山湖大道', '湖滨路', '淀山湖风景区',
            '淀山湖新区', '湖光路'
        ],
        
        # 万达商圈
        '万达商圈': [
            '万达', '万达广场', '万达金街', '前进西路', '万达茂', '万达影城',
            '万达商业广场', '万达购物中心', '前进路万达'
        ],
        
        # 吾悦商圈
        '吾悦商圈': [
            '吾悦', '吾悦广场', '萧林路', '吾悦购物中心', '新城吾悦',
            '吾悦商业广场', '萧林东路', '萧林西路'
        ],
        
        # 城西商圈
        '城西商圈': [
            '城西', '西环路', '马鞍山路', '柏庐路', '西部', '城西新区',
            '西城', '柏庐新村', '西门'
        ],
        
        # 城东商圈
        '城东商圈': [
            '城东', '东环路', '黄河路', '长江路', '东部', '城东新区',
            '东城', '东门', '黄浦江路'
        ],
        
        # 高新区
        '高新区': [
            '高新区', '科技园', '创业园', '软件园', '高新技术', '科创园',
            '创新园', '孵化器', '研发中心', '科技城', '高科技'
        ],
        
        # 开发区
        '开发区': [
            '开发区', '经济开发区', '富士康', '纬一路', '纬二路', '经一路',
            '经二路', '工业园', '制造业基地', '出口加工区'
        ],
        
        # 周边镇区补充
        '周边镇区': [
            '石牌', '石牌镇', '正仪', '正仪镇', '蓬朗', '蓬朗镇',
            '淞南', '淞南镇', '大市', '大市镇'
        ]
    }
    
    return area_mapping

def extract_location_keywords(text):
    """从文本中提取位置关键词"""
    if pd.isna(text):
        return []
    
    text = str(text).lower()
    
    # 提取常见的地址关键词
    location_patterns = [
        r'(\w+路)\d*号?',  # 路名
        r'(\w+街)\d*号?',  # 街名
        r'(\w+镇)',       # 镇名
        r'(\w+区)',       # 区名
        r'(\w+村)',       # 村名
        r'(\w+新村)',     # 新村
        r'(\w+花园)',     # 花园
        r'(\w+广场)',     # 广场
        r'(\w+中心)',     # 中心
        r'(\w+大道)',     # 大道
    ]
    
    keywords = []
    for pattern in location_patterns:
        matches = re.findall(pattern, text)
        keywords.extend(matches)
    
    # 添加原始文本中的关键词
    keywords.append(text)
    
    return keywords

def classify_business_area_enhanced(shop_name, address, current_area):
    """基于昆山地理数据库的精确商圈分类"""

    # 如果当前分类不是问题分类，保持不变
    if current_area not in ['未知商圈', '其他商圈']:
        return current_area, "保持原分类"

    # 合并店铺名称和地址信息
    combined_text = f"{shop_name} {address}".lower()

    # 获取地理数据库
    location_db = get_kunshan_location_database()
    special_keywords = get_special_keywords()
    road_patterns = get_road_patterns()

    # 第一步：道路模式匹配（最高优先级）
    for pattern, area in road_patterns['road_patterns'].items():
        if re.search(pattern, combined_text):
            return area, f"道路模式匹配: {pattern}"

    # 第二步：特殊关键词匹配（商业综合体、交通枢纽等）
    for category, area_keywords in special_keywords.items():
        for area, keywords in area_keywords.items():
            for keyword in keywords:
                if keyword.lower() in combined_text:
                    return area, f"特殊地标匹配: {keyword}"

    # 第三步：详细地理数据库匹配
    match_scores = {}

    for area, area_info in location_db.items():
        score = 0
        matched_items = []

        # 主要道路匹配（权重最高）
        for road in area_info['main_roads']:
            if road.lower() in combined_text:
                score += 15
                matched_items.append(f"道路:{road}")

        # 地标匹配（权重中等）
        for landmark in area_info['landmarks']:
            if landmark.lower() in combined_text:
                score += 12
                matched_items.append(f"地标:{landmark}")

        # 区域名称匹配（权重较低）
        for area_name in area_info['areas']:
            if area_name.lower() in combined_text:
                score += 8
                matched_items.append(f"区域:{area_name}")

        # 部分匹配加分
        for road in area_info['main_roads']:
            road_base = road.replace('路', '').replace('街', '').replace('大道', '')
            if len(road_base) > 1 and road_base.lower() in combined_text:
                score += 5
                matched_items.append(f"部分:{road_base}")

        if score > 0:
            match_scores[area] = {
                'score': score,
                'items': matched_items
            }

    # 选择得分最高的商圈
    if match_scores:
        best_area = max(match_scores.keys(), key=lambda x: match_scores[x]['score'])
        best_score = match_scores[best_area]['score']
        matched_items = match_scores[best_area]['items']

        # 设置置信度阈值
        if best_score >= 15:  # 有主要道路匹配
            return best_area, f"高置信度匹配: {', '.join(matched_items[:2])}"
        elif best_score >= 12:  # 有地标匹配
            return best_area, f"地标匹配: {', '.join(matched_items[:2])}"
        elif best_score >= 8:   # 有区域名称匹配
            return best_area, f"区域匹配: {', '.join(matched_items[:2])}"

    # 第四步：模糊匹配和默认分类
    fuzzy_keywords = {
        '玉山商圈': ['市区', '中心', '城区', '老城', '市政', '政府', '广场'],
        '花桥商圈': ['花桥', '国际', '商务', '地铁', '11号'],
        '万达商圈': ['万达', 'wanda'],
        '吾悦商圈': ['吾悦', '新城'],
        '高新区': ['科技', '创新', '研发', '高新', '园区'],
        '开发区': ['工业', '制造', '厂区', '开发', '富士康']
    }

    for area, keywords in fuzzy_keywords.items():
        for keyword in keywords:
            if keyword in combined_text:
                return area, f"模糊匹配: {keyword}"

    # 第五步：基于地址特征的默认分类
    if any(word in combined_text for word in ['路', '街', '巷', '弄', '号']):
        # 如果有具体地址但无法匹配，默认为市中心
        return '玉山商圈', "有地址信息，默认市中心"

    # 最后保持原分类
    return current_area, "无法确定具体位置"

def fix_business_areas(df):
    """修正商圈分类"""
    print(f"\n=== 开始修正商圈分类 ===")
    
    # 统计需要修正的店铺
    problem_shops = df[df['商圈'].isin(['未知商圈', '其他商圈'])].copy()
    print(f"需要重新分类的店铺数量: {len(problem_shops)}")
    
    if len(problem_shops) == 0:
        print("✅ 没有需要修正的商圈分类")
        return df
    
    # 逐个处理问题店铺
    corrections = []
    
    for idx, row in problem_shops.iterrows():
        shop_name = row.get('店铺名称', '')
        address = row.get('地址', '')
        current_area = row.get('商圈', '')
        
        new_area, reason = classify_business_area_enhanced(shop_name, address, current_area)
        
        if new_area != current_area:
            corrections.append({
                'index': idx,
                'shop_name': shop_name,
                'address': address,
                'old_area': current_area,
                'new_area': new_area,
                'reason': reason
            })
            
            # 更新数据
            df.loc[idx, '商圈'] = new_area
            
            # 同时更新商圈等级
            area_levels = {
                '万达商圈': '核心商圈', '吾悦商圈': '核心商圈', '玉山商圈': '核心商圈',
                '朝阳商圈': '重要商圈', '花桥商圈': '重要商圈', '陆家商圈': '重要商圈',
                '周市商圈': '重要商圈', '千灯商圈': '重要商圈', '高新区': '重要商圈',
                '开发区': '重要商圈'
            }
            df.loc[idx, '商圈等级'] = area_levels.get(new_area, '一般商圈')
    
    # 显示修正结果
    print(f"\n=== 修正结果 ===")
    print(f"成功修正: {len(corrections)}家店铺")
    
    if corrections:
        print(f"\n修正详情:")
        for i, correction in enumerate(corrections[:10], 1):  # 只显示前10个
            print(f"{i:2d}. {correction['shop_name'][:20]:<20} "
                  f"{correction['old_area']} → {correction['new_area']} "
                  f"({correction['reason'][:30]})")
        
        if len(corrections) > 10:
            print(f"    ... 还有 {len(corrections) - 10} 家店铺被修正")
    
    return df, corrections

def generate_correction_report(corrections, df):
    """生成修正报告"""
    print(f"\n=== 生成修正报告 ===")
    
    # 统计修正后的商圈分布
    new_area_counts = df['商圈'].value_counts()
    
    report_content = f"""# 商圈分类修正报告

## 修正概述
- 修正时间: 2024年12月
- 修正店铺数量: {len(corrections)}家
- 修正方法: 基于店铺名称和地址的智能匹配

## 修正后商圈分布
"""
    
    for area, count in new_area_counts.items():
        status = "✅" if area not in ['未知商圈', '其他商圈'] else "❌"
        report_content += f"{status} {area}: {count}家\n"
    
    report_content += f"""
## 详细修正记录
"""
    
    for i, correction in enumerate(corrections, 1):
        report_content += f"""
### {i}. {correction['shop_name']}
- **地址**: {correction['address']}
- **修正**: {correction['old_area']} → {correction['new_area']}
- **依据**: {correction['reason']}
"""
    
    # 保存报告
    with open('商圈分类修正报告.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✓ 修正报告已保存到: 商圈分类修正报告.md")

def main():
    """主函数"""
    # 1. 加载数据
    df = load_data()
    if df is None:
        return
    
    # 2. 分析问题商圈
    problem_count = analyze_problem_areas(df)
    
    if problem_count == 0:
        print("✅ 所有店铺商圈分类正确，无需修正")
        return
    
    # 3. 修正商圈分类
    df_fixed, corrections = fix_business_areas(df)
    
    # 4. 保存修正后的数据
    output_file = '昆山奶茶店_商圈修正后数据.xlsx'
    df_fixed.to_excel(output_file, index=False)
    print(f"✓ 修正后数据已保存到: {output_file}")
    
    # 5. 生成修正报告
    if corrections:
        generate_correction_report(corrections, df_fixed)
    
    # 6. 显示最终统计
    print(f"\n=== 最终商圈分布 ===")
    final_counts = df_fixed['商圈'].value_counts()
    remaining_problems = final_counts.get('未知商圈', 0) + final_counts.get('其他商圈', 0)
    
    for area, count in final_counts.items():
        status = "✅" if area not in ['未知商圈', '其他商圈'] else "❌"
        print(f"{status} {area}: {count}家")
    
    print(f"\n✅ 商圈修正完成!")
    print(f"✅ 剩余问题商圈: {remaining_problems}家")
    print(f"✅ 修正成功率: {((problem_count - remaining_problems) / problem_count * 100):.1f}%")
    
    print(f"\n下一步操作:")
    print(f"1. 使用修正后的数据文件: {output_file}")
    print(f"2. 导入FineBI进行分析")
    print(f"3. 查看修正报告了解详情")

if __name__ == "__main__":
    main()
