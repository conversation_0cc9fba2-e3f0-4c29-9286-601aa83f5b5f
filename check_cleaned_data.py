#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查清洗后的数据
"""

try:
    import pandas as pd
    
    print("=== 检查清洗后的数据 ===")
    
    # 读取清洗后的数据
    df = pd.read_excel('昆山奶茶店_清洗后数据.xlsx')
    
    print(f"✓ 清洗后数据: {df.shape[0]} 行 × {df.shape[1]} 列")
    
    print(f"\n字段列表:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i:2d}. {col}")
    
    print(f"\n前5行数据预览:")
    print(df.head().to_string())
    
    print(f"\n数据统计:")
    
    # 商圈分布
    if '商圈' in df.columns:
        print(f"\n商圈分布:")
        for area, count in df['商圈'].value_counts().items():
            print(f"  {area}: {count}家")
    
    # 价格区间分布
    if '价格区间' in df.columns:
        print(f"\n价格区间分布:")
        for price, count in df['价格区间'].value_counts().items():
            print(f"  {price}: {count}家")
    
    # 评分等级分布
    if '评分等级' in df.columns:
        print(f"\n评分等级分布:")
        for level, count in df['评分等级'].value_counts().items():
            print(f"  {level}: {count}家")
    
    # 品牌类型分布
    if '品牌类型' in df.columns:
        print(f"\n品牌类型分布:")
        for brand, count in df['品牌类型'].value_counts().items():
            print(f"  {brand}: {count}家")
    
    # 商圈等级分布
    if '商圈等级' in df.columns:
        print(f"\n商圈等级分布:")
        for level, count in df['商圈等级'].value_counts().items():
            print(f"  {level}: {count}家")
    
    # 评分统计
    if '评分' in df.columns:
        print(f"\n评分统计:")
        print(f"  平均评分: {df['评分'].mean():.2f}")
        print(f"  最高评分: {df['评分'].max()}")
        print(f"  最低评分: {df['评分'].min()}")
    
    # 价格统计
    if '人均消费' in df.columns:
        print(f"\n价格统计:")
        print(f"  平均消费: {df['人均消费'].mean():.2f}元")
        print(f"  最高消费: {df['人均消费'].max()}元")
        print(f"  最低消费: {df['人均消费'].min()}元")
    
    print(f"\n✓ 数据检查完成！")
    print(f"✓ 数据质量良好，可以用于FineBI分析")
    
except Exception as e:
    print(f"❌ 检查数据时出错: {e}")
