#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版数据清洗脚本
"""

try:
    import pandas as pd
    import re
    print("=== 昆山奶茶店数据清洗工具 ===")
    print("正在启动...")
    
    # 读取数据
    print("\n1. 读取Excel文件...")
    file_path = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"
    df = pd.read_excel(file_path)
    print(f"✓ 成功读取 {df.shape[0]} 行 × {df.shape[1]} 列数据")
    
    # 显示列名
    print(f"\n2. 数据结构:")
    for i, col in enumerate(df.columns, 1):
        print(f"   {i:2d}. {col}")
    
    # 基础清洗
    print(f"\n3. 开始数据清洗...")
    df_clean = df.copy()
    
    # 删除重复行
    before = len(df_clean)
    df_clean = df_clean.drop_duplicates()
    after = len(df_clean)
    print(f"   删除重复行: {before - after} 行")
    
    # 删除空行
    before = len(df_clean)
    df_clean = df_clean.dropna(how='all')
    after = len(df_clean)
    print(f"   删除空行: {before - after} 行")
    
    # 处理核心字段
    print(f"\n4. 处理核心字段...")
    
    # 店铺名称
    if '标题' in df_clean.columns:
        df_clean['店铺名称'] = df_clean['标题'].astype(str).str.strip()
        print(f"   ✓ 处理店铺名称")
    
    # 地址信息
    if '地址' in df_clean.columns:
        df_clean['地址_清洗'] = df_clean['地址'].astype(str).str.strip()
        
        # 简单的商圈分类
        def get_area(address):
            if pd.isna(address) or str(address) == 'nan':
                return '未知商圈'
            
            address = str(address)
            if '朝阳' in address:
                return '朝阳商圈'
            elif '玉山' in address:
                return '玉山商圈'
            elif '花桥' in address:
                return '花桥商圈'
            elif '陆家' in address:
                return '陆家商圈'
            elif '周市' in address:
                return '周市商圈'
            elif '万达' in address:
                return '万达商圈'
            elif '中心' in address:
                return '市中心'
            else:
                return '其他商圈'
        
        df_clean['商圈'] = df_clean['地址_清洗'].apply(get_area)
        print(f"   ✓ 提取商圈信息")
    
    # 价格信息
    if '价格' in df_clean.columns:
        def extract_price(price_str):
            if pd.isna(price_str) or str(price_str) == 'nan':
                return None
            
            # 提取数字
            numbers = re.findall(r'\d+', str(price_str))
            if numbers:
                return int(numbers[0])
            return None
        
        df_clean['人均消费'] = df_clean['价格'].apply(extract_price)
        
        # 价格分级
        def price_category(price):
            if pd.isna(price):
                return '未知价格'
            elif price <= 20:
                return '经济型(≤20元)'
            elif price <= 35:
                return '中档型(21-35元)'
            elif price <= 50:
                return '高档型(36-50元)'
            else:
                return '奢华型(>50元)'
        
        df_clean['价格区间'] = df_clean['人均消费'].apply(price_category)
        print(f"   ✓ 处理价格信息")
    
    # 推荐信息（评分）
    if '推荐' in df_clean.columns:
        def extract_rating(text):
            if pd.isna(text) or str(text) == 'nan':
                return None
            
            # 查找评分
            patterns = [r'(\d+\.?\d*)\s*分', r'(\d+\.?\d*)\s*星']
            for pattern in patterns:
                match = re.search(pattern, str(text))
                if match:
                    rating = float(match.group(1))
                    if 0 <= rating <= 5:
                        return rating
            return None
        
        df_clean['评分'] = df_clean['推荐'].apply(extract_rating)
        
        # 评分等级
        def rating_level(rating):
            if pd.isna(rating):
                return '暂无评分'
            elif rating >= 4.5:
                return '优秀(4.5-5.0)'
            elif rating >= 4.0:
                return '良好(4.0-4.4)'
            elif rating >= 3.5:
                return '一般(3.5-3.9)'
            else:
                return '较差(<3.5)'
        
        df_clean['评分等级'] = df_clean['评分'].apply(rating_level)
        print(f"   ✓ 提取评分信息")
    
    # 创建店铺ID
    df_clean['店铺ID'] = 'SHOP_' + (df_clean.index + 1).astype(str).str.zfill(3)
    
    # 选择要保存的字段
    save_columns = ['店铺ID', '店铺名称', '地址_清洗', '商圈', '人均消费', '价格区间', '评分', '评分等级']
    save_columns = [col for col in save_columns if col in df_clean.columns]
    
    # 重命名字段
    df_final = df_clean[save_columns].copy()
    if '地址_清洗' in df_final.columns:
        df_final = df_final.rename(columns={'地址_清洗': '地址'})
    
    # 保存清洗后的数据
    output_file = '昆山奶茶店_清洗后数据.xlsx'
    df_final.to_excel(output_file, index=False)
    
    print(f"\n5. 清洗完成!")
    print(f"   ✓ 清洗后数据: {len(df_final)} 行 × {len(df_final.columns)} 列")
    print(f"   ✓ 保存到: {output_file}")
    
    # 显示统计信息
    print(f"\n6. 数据统计:")
    if '商圈' in df_final.columns:
        print("   商圈分布:")
        for area, count in df_final['商圈'].value_counts().head().items():
            print(f"     {area}: {count}家")
    
    if '价格区间' in df_final.columns:
        print("   价格分布:")
        for price, count in df_final['价格区间'].value_counts().head().items():
            print(f"     {price}: {count}家")
    
    print(f"\n✓ 数据清洗成功完成!")
    print(f"✓ 可以使用 '{output_file}' 进行FineBI分析")
    
except ImportError as e:
    print(f"❌ 缺少必要的库: {e}")
    print("请安装pandas: pip install pandas openpyxl")
except FileNotFoundError:
    print("❌ 找不到Excel文件，请确认文件名和路径正确")
except Exception as e:
    print(f"❌ 处理过程中出错: {e}")
    print("请检查数据文件格式是否正确")
