#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
昆山地理位置数据库
基于实际昆山地理信息的详细位置映射
"""

def get_kunshan_location_database():
    """获取昆山详细地理位置数据库"""
    
    location_db = {
        # 玉山镇（市中心）- 昆山市政府所在地
        '玉山商圈': {
            'main_roads': [
                '人民路', '前进路', '同心路', '柏庐路', '亭林路', '震川路', 
                '马鞍山路', '娄江路', '玉山南路', '玉山北路', '中山路',
                '白马泾路', '青阳路', '长江路', '黄河路'
            ],
            'landmarks': [
                '人民广场', '玉山广场', '昆山站', '火车站', '客运站',
                '市政府', '市中心', '中心医院', '第一人民医院',
                '昆山宾馆', '金鹰购物中心', '大润发', '欧尚'
            ],
            'areas': [
                '玉山', '玉山镇', '市区', '城区', '老城区', '市中心',
                '城中', '新客站', '老客站', '玉山新村', '柏庐新村'
            ]
        },
        
        # 朝阳商圈
        '朝阳商圈': {
            'main_roads': [
                '朝阳路', '朝阳东路', '朝阳西路', '朝阳中路', '朝阳南路',
                '朝阳北路', '朝阳大道'
            ],
            'landmarks': [
                '朝阳门', '朝阳广场', '朝阳医院', '朝阳学校'
            ],
            'areas': [
                '朝阳', '朝阳街', '朝阳新村', '朝阳花园', '朝阳小区',
                '朝阳社区', '朝阳镇'
            ]
        },
        
        # 花桥商圈 - 国际商务城
        '花桥商圈': {
            'main_roads': [
                '绿地大道', '花安路', '徐公桥路', '花溪路', '兆丰路',
                '横泾路', '集善路', '花桥路', '商务大道', '国际大道'
            ],
            'landmarks': [
                '花桥国际商务城', '花桥地铁站', '11号线', '绿地21城',
                '花桥中心', '花桥新城', '花桥经济开发区', '花桥站'
            ],
            'areas': [
                '花桥', '花桥镇', '花桥新区', '花桥老街', '商务城',
                '国际商务城', '花桥经开区'
            ]
        },
        
        # 陆家商圈
        '陆家商圈': {
            'main_roads': [
                '陆丰路', '菉溪路', '陆家浜路', '金阳路', '陆杨路',
                '陆家大道', '陆家中心路'
            ],
            'landmarks': [
                '陆家中心', '陆家医院', '陆家学校', '陆家广场'
            ],
            'areas': [
                '陆家', '陆家镇', '陆家新区', '陆家老街', '陆家新村',
                '陆家社区'
            ]
        },
        
        # 周市商圈
        '周市商圈': {
            'main_roads': [
                '白塘路', '横长泾路', '金浦路', '青阳路', '市北路',
                '周市大道', '周市中心路'
            ],
            'landmarks': [
                '周市中心', '周市医院', '周市学校', '周市广场'
            ],
            'areas': [
                '周市', '周市镇', '周市新镇', '周市老街', '周市新区',
                '白塘', '横长泾'
            ]
        },
        
        # 千灯商圈 - 古镇
        '千灯商圈': {
            'main_roads': [
                '石浦路', '昆山大道', '炎武路', '淞南路', '千灯大道',
                '古镇路', '千灯中心路'
            ],
            'landmarks': [
                '千灯古镇', '千灯中心', '千灯医院', '千灯学校',
                '顾炎武故居', '千灯镇政府'
            ],
            'areas': [
                '千灯', '千灯镇', '千灯新区', '千灯古镇', '石浦',
                '炎武', '古镇区'
            ]
        },
        
        # 巴城商圈 - 阳澄湖
        '巴城商圈': {
            'main_roads': [
                '湖滨路', '正仪路', '巴解路', '阳澄湖路', '湖光路',
                '巴城大道', '湖滨大道'
            ],
            'landmarks': [
                '阳澄湖', '巴城中心', '巴城医院', '阳澄湖服务区',
                '湖滨公园', '正仪古镇'
            ],
            'areas': [
                '巴城', '巴城镇', '正仪', '正仪镇', '阳澄湖镇',
                '湖滨新村', '巴城老街', '湖区'
            ]
        },
        
        # 张浦商圈
        '张浦商圈': {
            'main_roads': [
                '振新路', '港浦路', '振兴路', '浦东路', '张浦大道',
                '张浦中心路', '港区路'
            ],
            'landmarks': [
                '张浦中心', '张浦医院', '张浦学校', '张浦港'
            ],
            'areas': [
                '张浦', '张浦镇', '张浦新区', '张浦老街', '港浦',
                '振新', '振兴'
            ]
        },
        
        # 锦溪商圈 - 古镇
        '锦溪商圈': {
            'main_roads': [
                '邵甸港路', '锦东路', '锦西路', '古镇路', '锦溪大道',
                '锦溪中心路'
            ],
            'landmarks': [
                '锦溪古镇', '锦溪中心', '锦溪医院', '古莲桥',
                '锦溪镇政府'
            ],
            'areas': [
                '锦溪', '锦溪镇', '锦溪古镇', '邵甸港', '古镇区',
                '锦东', '锦西'
            ]
        },
        
        # 淀山湖商圈
        '淀山湖商圈': {
            'main_roads': [
                '淀山湖大道', '湖滨路', '湖光路', '淀山湖路',
                '风景区路'
            ],
            'landmarks': [
                '淀山湖', '淀山湖风景区', '淀山湖新区', '湖滨公园'
            ],
            'areas': [
                '淀山湖', '淀山湖镇', '淀山湖新区', '湖区', '风景区'
            ]
        },
        
        # 万达商圈 - 购物中心
        '万达商圈': {
            'main_roads': [
                '前进西路', '前进路', '万达路', '商业街'
            ],
            'landmarks': [
                '万达广场', '万达金街', '万达茂', '万达影城',
                '万达商业广场', '万达购物中心', '万达酒店'
            ],
            'areas': [
                '万达', '万达广场', '万达商圈', '前进西路万达'
            ]
        },
        
        # 吾悦商圈 - 新城控股
        '吾悦商圈': {
            'main_roads': [
                '萧林路', '萧林东路', '萧林西路', '吾悦路'
            ],
            'landmarks': [
                '吾悦广场', '吾悦购物中心', '新城吾悦', '吾悦商业广场',
                '吾悦影城', '吾悦酒店'
            ],
            'areas': [
                '吾悦', '吾悦广场', '新城吾悦', '萧林'
            ]
        },
        
        # 城西商圈
        '城西商圈': {
            'main_roads': [
                '西环路', '马鞍山路', '柏庐路', '西门路', '城西大道'
            ],
            'landmarks': [
                '城西新区', '西门', '柏庐新村', '城西医院'
            ],
            'areas': [
                '城西', '西部', '城西新区', '西城', '柏庐', '西门'
            ]
        },
        
        # 城东商圈
        '城东商圈': {
            'main_roads': [
                '东环路', '黄河路', '长江路', '东门路', '城东大道',
                '黄浦江路'
            ],
            'landmarks': [
                '城东新区', '东门', '城东医院', '东部新城'
            ],
            'areas': [
                '城东', '东部', '城东新区', '东城', '东门', '黄河',
                '长江', '黄浦江'
            ]
        },
        
        # 高新区 - 科技园区
        '高新区': {
            'main_roads': [
                '科技路', '创新路', '高新路', '园区路', '研发路'
            ],
            'landmarks': [
                '科技园', '创业园', '软件园', '高新技术产业园',
                '科创园', '创新园', '孵化器', '研发中心', '科技城'
            ],
            'areas': [
                '高新区', '科技园区', '高新技术区', '创新区',
                '科技城', '软件园区'
            ]
        },
        
        # 开发区 - 工业区
        '开发区': {
            'main_roads': [
                '纬一路', '纬二路', '纬三路', '经一路', '经二路',
                '开发区大道', '工业路'
            ],
            'landmarks': [
                '经济开发区', '富士康', '工业园', '制造业基地',
                '出口加工区', '保税区', '开发区管委会'
            ],
            'areas': [
                '开发区', '经济开发区', '工业园区', '制造基地',
                '加工区', '保税区', '富士康园区'
            ]
        }
    }
    
    return location_db

def get_special_keywords():
    """获取特殊关键词映射"""
    return {
        # 商业综合体
        '商业综合体': {
            '万达商圈': ['万达', 'wanda', '万达广场', '万达金街'],
            '吾悦商圈': ['吾悦', '新城', '吾悦广场'],
            '玉山商圈': ['金鹰', '大润发', '欧尚', '苏宁', '国美']
        },
        
        # 交通枢纽
        '交通枢纽': {
            '玉山商圈': ['火车站', '昆山站', '客运站', '汽车站'],
            '花桥商圈': ['地铁站', '11号线', '花桥站', '轨交']
        },
        
        # 医院学校
        '公共设施': {
            '玉山商圈': ['第一人民医院', '中心医院', '市医院'],
            '花桥商圈': ['花桥医院', '国际学校'],
            '朝阳商圈': ['朝阳医院', '朝阳学校']
        },
        
        # 住宅区
        '住宅区': {
            '玉山商圈': ['柏庐新村', '玉山新村', '中心新村'],
            '朝阳商圈': ['朝阳新村', '朝阳花园'],
            '巴城商圈': ['湖滨新村', '阳澄湖新村']
        }
    }

def get_road_patterns():
    """获取道路模式匹配"""
    return {
        # 主要道路特征
        'road_patterns': {
            r'人民路\d*号?': '玉山商圈',
            r'前进路\d*号?': '玉山商圈', 
            r'朝阳.*路\d*号?': '朝阳商圈',
            r'绿地大道\d*号?': '花桥商圈',
            r'花安路\d*号?': '花桥商圈',
            r'陆丰路\d*号?': '陆家商圈',
            r'白塘路\d*号?': '周市商圈',
            r'石浦路\d*号?': '千灯商圈',
            r'湖滨路\d*号?': '巴城商圈',
            r'振新路\d*号?': '张浦商圈',
            r'萧林路\d*号?': '吾悦商圈',
            r'西环路\d*号?': '城西商圈',
            r'东环路\d*号?': '城东商圈'
        }
    }

if __name__ == "__main__":
    # 测试数据库
    db = get_kunshan_location_database()
    print("昆山地理位置数据库加载完成")
    print(f"包含 {len(db)} 个商圈的详细信息")
    
    for area, info in db.items():
        total_keywords = len(info['main_roads']) + len(info['landmarks']) + len(info['areas'])
        print(f"- {area}: {total_keywords} 个关键词")
