# FineBI操作指南 - 奶茶店数据分析仪表盘制作

## 1. FineBI环境准备

### 1.1 软件安装
1. 下载FineBI安装包
2. 按照安装向导完成安装
3. 启动FineBI服务
4. 浏览器访问：http://localhost:37799/webroot/decision

### 1.2 初始配置
1. 创建管理员账户
2. 配置数据库连接（如需要）
3. 设置系统参数

## 2. 数据导入与连接

### 2.1 创建数据连接
1. 进入"管理系统" → "数据连接"
2. 点击"新建数据连接"
3. 选择"Excel"类型
4. 上传清洗后的Excel文件
5. 测试连接并保存

### 2.2 创建数据集
1. 进入"数据准备" → "业务包"
2. 点击"新建业务包"
3. 选择已创建的数据连接
4. 选择Excel文件中的工作表
5. 预览数据并确认字段类型

### 2.3 字段配置
```
字段配置示例：
- 店铺名称：文本类型，维度
- 地址：文本类型，维度
- 商圈：文本类型，维度
- 评分：数值类型，指标
- 人均消费：数值类型，指标
- 评论数：数值类型，指标
- 评分等级：文本类型，维度
- 价格区间：文本类型，维度
```

## 3. 数据建模

### 3.1 创建计算字段
1. 在业务包中点击"新建计算字段"
2. 创建以下计算字段：

#### 高评分店铺标识
```sql
IF(评分 >= 4.5, "高评分", "普通")
```

#### 性价比指标
```sql
评分 / 人均消费 * 10
```

#### 商圈热度
```sql
COUNT(店铺名称) BY 商圈
```

### 3.2 数据关联
如果有多个数据表，建立表间关联关系：
1. 拖拽字段建立关联
2. 设置关联类型（内连接、左连接等）
3. 验证关联结果

## 4. 图表制作

### 4.1 核心指标卡片

#### 4.1.1 总店铺数
1. 新建组件 → 指标卡
2. 拖入"店铺名称"到指标区
3. 选择聚合方式：计数
4. 设置标题："总店铺数"
5. 调整样式和颜色

#### 4.1.2 平均评分
1. 新建组件 → 指标卡
2. 拖入"评分"到指标区
3. 选择聚合方式：平均值
4. 设置小数位数：1位
5. 设置标题："平均评分"

#### 4.1.3 平均消费
1. 新建组件 → 指标卡
2. 拖入"人均消费"到指标区
3. 选择聚合方式：平均值
4. 设置单位："元"
5. 设置标题："平均消费"

### 4.2 地理分布分析

#### 4.2.1 商圈分布柱状图
1. 新建组件 → 柱状图
2. 拖入"商圈"到维度区
3. 拖入"店铺名称"到指标区（计数）
4. 设置图表标题："各商圈店铺数量分布"
5. 调整颜色和样式

#### 4.2.2 地图分布（如有经纬度数据）
1. 新建组件 → 地图
2. 拖入"经度"和"纬度"
3. 拖入"评分"作为气泡大小
4. 设置地图类型和样式

### 4.3 评分分析

#### 4.3.1 评分分布直方图
1. 新建组件 → 柱状图
2. 拖入"评分等级"到维度区
3. 拖入"店铺名称"到指标区（计数）
4. 设置图表标题："评分等级分布"
5. 按评分高低排序

#### 4.3.2 评分与价格散点图
1. 新建组件 → 散点图
2. 拖入"人均消费"到X轴
3. 拖入"评分"到Y轴
4. 拖入"商圈"到颜色
5. 设置标题："价格与评分关系"

### 4.4 价格分析

#### 4.4.1 价格区间分布
1. 新建组件 → 饼图
2. 拖入"价格区间"到维度区
3. 拖入"店铺名称"到指标区（计数）
4. 设置图表标题："价格区间分布"
5. 显示百分比

#### 4.4.2 各商圈平均价格
1. 新建组件 → 柱状图
2. 拖入"商圈"到维度区
3. 拖入"人均消费"到指标区（平均值）
4. 设置图表标题："各商圈平均消费水平"
5. 按价格高低排序

### 4.5 排行榜

#### 4.5.1 评分TOP10
1. 新建组件 → 表格
2. 拖入"店铺名称"、"评分"、"人均消费"、"商圈"
3. 按评分降序排列
4. 设置显示前10条
5. 设置条件格式突出高分

#### 4.5.2 人气TOP10（按评论数）
1. 新建组件 → 表格
2. 拖入"店铺名称"、"评论数"、"评分"、"商圈"
3. 按评论数降序排列
4. 设置显示前10条

## 5. 仪表盘组装

### 5.1 创建仪表盘
1. 进入"仪表盘" → "新建仪表盘"
2. 选择合适的模板或空白模板
3. 设置仪表盘名称："昆山奶茶店数据分析"

### 5.2 布局设计
```
仪表盘布局（12列网格）：
+--------+--------+--------+--------+
|  总店铺数  |  平均评分  |  平均消费  |  最热商圈  |
|   (3列)   |   (3列)   |   (3列)   |   (3列)   |
+--------+--------+--------+--------+
|           商圈分布柱状图              |
|              (12列)                |
+--------+--------+--------+--------+
| 评分分布图 | 价格分布图 | 价格评分散点图 |
|   (4列)   |   (4列)   |    (4列)    |
+--------+--------+--------+--------+
|           评分TOP10排行榜             |
|              (12列)                |
+--------+--------+--------+--------+
```

### 5.3 添加组件
1. 从组件库拖拽已创建的图表到仪表盘
2. 调整组件大小和位置
3. 设置组件间距和对齐

### 5.4 配置筛选器
1. 添加"商圈"筛选器
2. 添加"价格区间"筛选器
3. 添加"评分等级"筛选器
4. 设置筛选器样式和位置

## 6. 交互功能配置

### 6.1 组件联动
1. 选择源组件（如商圈分布图）
2. 设置联动目标组件
3. 配置联动字段和方式
4. 测试联动效果

### 6.2 钻取功能
1. 在商圈分布图上设置钻取
2. 配置钻取路径：商圈 → 具体店铺
3. 设置钻取样式

### 6.3 跳转功能
1. 在排行榜中设置跳转
2. 跳转到店铺详情页面
3. 传递参数

## 7. 样式美化

### 7.1 主题设置
1. 选择合适的颜色主题
2. 设置统一的字体和字号
3. 调整组件边框和阴影

### 7.2 颜色配置
```
建议配色方案：
- 主色调：#1890FF（蓝色）
- 辅助色：#52C41A（绿色）
- 警告色：#FAAD14（橙色）
- 危险色：#F5222D（红色）
- 中性色：#8C8C8C（灰色）
```

### 7.3 图表优化
1. 设置合适的图例位置
2. 调整坐标轴标签
3. 添加数据标签
4. 设置动画效果

## 8. 发布与分享

### 8.1 预览测试
1. 点击"预览"按钮
2. 测试所有交互功能
3. 检查在不同设备上的显示效果
4. 验证数据准确性

### 8.2 发布仪表盘
1. 点击"发布"按钮
2. 设置访问权限
3. 生成分享链接
4. 配置定时刷新

### 8.3 移动端适配
1. 检查移动端显示效果
2. 调整组件大小和布局
3. 优化触摸交互

## 9. 维护与优化

### 9.1 数据更新
1. 设置数据源自动更新
2. 定期检查数据质量
3. 更新计算字段逻辑

### 9.2 性能优化
1. 优化查询语句
2. 设置合理的缓存策略
3. 监控仪表盘加载速度

### 9.3 用户反馈
1. 收集用户使用反馈
2. 根据需求调整功能
3. 持续改进用户体验

## 10. 常见问题解决

### 10.1 数据显示问题
- 检查字段类型设置
- 验证数据源连接
- 确认筛选条件

### 10.2 性能问题
- 减少数据量
- 优化计算字段
- 使用数据抽取

### 10.3 样式问题
- 检查浏览器兼容性
- 调整组件大小
- 重新设置样式

通过以上步骤，您就能够创建一个功能完整、美观实用的奶茶店数据分析仪表盘了！
