#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗脚本 - 清洗八爪鱼爬取的大众点评奶茶店数据
专门针对昆山奶茶店数据进行清洗和预处理
"""

import pandas as pd
import numpy as np
import re
import warnings
warnings.filterwarnings('ignore')

print("=== 昆山奶茶店数据清洗工具 ===")
print("正在启动数据清洗程序...")

def load_and_analyze_data(file_path):
    """加载并分析原始数据"""
    try:
        print("\n1. 正在读取Excel文件...")
        df = pd.read_excel(file_path)

        print(f"✓ 数据加载成功！")
        print(f"  - 数据形状: {df.shape[0]} 行 × {df.shape[1]} 列")

        print(f"\n2. 数据结构分析:")
        print("   列名及数据类型:")
        for i, col in enumerate(df.columns, 1):
            non_null_count = df[col].count()
            null_count = df[col].isnull().sum()
            print(f"   {i:2d}. {col:<15} - 非空:{non_null_count:3d} 缺失:{null_count:3d}")

        print(f"\n3. 数据质量检查:")
        total_missing = df.isnull().sum().sum()
        duplicate_rows = df.duplicated().sum()
        print(f"   - 总缺失值: {total_missing}")
        print(f"   - 重复行数: {duplicate_rows}")

        # 显示前几行数据样例
        print(f"\n4. 数据样例 (前3行):")
        for i, row in df.head(3).iterrows():
            print(f"   第{i+1}行:")
            for col in df.columns:
                if pd.notna(row[col]) and str(row[col]).strip():
                    print(f"     {col}: {str(row[col])[:50]}...")
            print()

        return df

    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return None

def clean_data(df):
    """专门针对奶茶店数据的清洗函数"""
    print("\n=== 开始数据清洗 ===")

    df_cleaned = df.copy()
    original_shape = df_cleaned.shape

    print(f"原始数据: {original_shape[0]} 行 × {original_shape[1]} 列")

    # 1. 删除完全重复的行
    print("\n1. 处理重复数据...")
    before_dup = len(df_cleaned)
    df_cleaned = df_cleaned.drop_duplicates()
    after_dup = len(df_cleaned)
    print(f"   删除重复行: {before_dup - after_dup} 行")

    # 2. 删除全为空的行
    print("\n2. 删除空行...")
    before_empty = len(df_cleaned)
    df_cleaned = df_cleaned.dropna(how='all')
    after_empty = len(df_cleaned)
    print(f"   删除空行: {before_empty - after_empty} 行")

    # 3. 处理核心字段
    print("\n3. 清洗核心字段...")

    # 清洗店铺名称（标题字段）
    if '标题' in df_cleaned.columns:
        print("   处理店铺名称...")
        # 删除标题为空的行
        before_title = len(df_cleaned)
        df_cleaned = df_cleaned.dropna(subset=['标题'])
        after_title = len(df_cleaned)
        print(f"     删除无店名记录: {before_title - after_title} 行")

        # 清理店铺名称格式
        df_cleaned['店铺名称'] = df_cleaned['标题'].astype(str).str.strip()
        df_cleaned['店铺名称'] = df_cleaned['店铺名称'].str.replace(r'\s+', ' ', regex=True)

    # 清洗价格信息
    if '价格' in df_cleaned.columns:
        print("   处理价格信息...")
        df_cleaned['价格_清洗'] = df_cleaned['价格'].astype(str)
        # 提取数字价格
        df_cleaned['人均消费'] = df_cleaned['价格_清洗'].str.extract(r'(\d+)').astype(float)

        # 处理价格区间
        def extract_price_range(price_str):
            if pd.isna(price_str) or price_str == 'nan':
                return None, None, None

            # 提取所有数字
            numbers = re.findall(r'\d+', str(price_str))
            if not numbers:
                return None, None, None

            numbers = [int(x) for x in numbers]
            if len(numbers) == 1:
                return numbers[0], numbers[0], numbers[0]
            elif len(numbers) >= 2:
                min_price = min(numbers)
                max_price = max(numbers)
                avg_price = (min_price + max_price) / 2
                return min_price, max_price, avg_price

            return None, None, None

        price_info = df_cleaned['价格_清洗'].apply(extract_price_range)
        df_cleaned['最低价格'] = [x[0] for x in price_info]
        df_cleaned['最高价格'] = [x[1] for x in price_info]
        df_cleaned['平均价格'] = [x[2] for x in price_info]

    # 清洗地址信息
    if '地址' in df_cleaned.columns:
        print("   处理地址信息...")
        df_cleaned['地址_清洗'] = df_cleaned['地址'].astype(str).str.strip()

        # 提取商圈信息（简单的关键词匹配）
        def extract_business_area(address):
            if pd.isna(address) or address == 'nan':
                return '未知商圈'

            address = str(address)
            # 常见昆山商圈关键词
            business_areas = {
                '朝阳': '朝阳商圈',
                '玉山': '玉山商圈',
                '花桥': '花桥商圈',
                '陆家': '陆家商圈',
                '周市': '周市商圈',
                '千灯': '千灯商圈',
                '巴城': '巴城商圈',
                '淀山湖': '淀山湖商圈',
                '锦溪': '锦溪商圈',
                '张浦': '张浦商圈',
                '中心': '市中心',
                '万达': '万达商圈',
                '吾悦': '吾悦商圈'
            }

            for keyword, area in business_areas.items():
                if keyword in address:
                    return area

            return '其他商圈'

        df_cleaned['商圈'] = df_cleaned['地址_清洗'].apply(extract_business_area)

    return df_cleaned

def create_derived_fields(df):
    """创建衍生字段"""
    print("\n4. 创建衍生字段...")

    # 价格分级
    if '平均价格' in df.columns:
        print("   创建价格分级...")
        def categorize_price(price):
            if pd.isna(price):
                return '未知价格'
            elif price <= 20:
                return '经济型(≤20元)'
            elif price <= 35:
                return '中档型(21-35元)'
            elif price <= 50:
                return '高档型(36-50元)'
            else:
                return '奢华型(>50元)'

        df['价格区间'] = df['平均价格'].apply(categorize_price)

    # 处理推荐信息（可能包含评分）
    if '推荐' in df.columns:
        print("   处理推荐信息...")
        df['推荐_清洗'] = df['推荐'].astype(str)

        # 尝试提取评分
        def extract_rating(recommend_str):
            if pd.isna(recommend_str) or recommend_str == 'nan':
                return None

            # 查找评分模式 (如 4.5分, 4.5星等)
            rating_patterns = [
                r'(\d+\.?\d*)\s*分',
                r'(\d+\.?\d*)\s*星',
                r'评分\s*(\d+\.?\d*)',
                r'(\d+\.?\d*)\s*/\s*5'
            ]

            for pattern in rating_patterns:
                match = re.search(pattern, str(recommend_str))
                if match:
                    rating = float(match.group(1))
                    if 0 <= rating <= 5:
                        return rating

            return None

        df['评分'] = df['推荐_清洗'].apply(extract_rating)

        # 评分分级
        def categorize_rating(rating):
            if pd.isna(rating):
                return '暂无评分'
            elif rating >= 4.5:
                return '优秀(4.5-5.0)'
            elif rating >= 4.0:
                return '良好(4.0-4.4)'
            elif rating >= 3.5:
                return '一般(3.5-3.9)'
            elif rating >= 3.0:
                return '较差(3.0-3.4)'
            else:
                return '很差(<3.0)'

        df['评分等级'] = df['评分'].apply(categorize_rating)

    # 创建店铺ID
    if '店铺名称' in df.columns:
        print("   创建店铺ID...")
        df['店铺ID'] = 'SHOP_' + (df.index + 1).astype(str).str.zfill(3)

    return df

def generate_cleaning_report(df_original, df_cleaned):
    """生成数据清洗报告"""
    print("\n=== 数据清洗报告 ===")

    print(f"原始数据: {df_original.shape[0]} 行 × {df_original.shape[1]} 列")
    print(f"清洗后数据: {df_cleaned.shape[0]} 行 × {df_cleaned.shape[1]} 列")
    print(f"删除行数: {df_original.shape[0] - df_cleaned.shape[0]}")
    print(f"新增列数: {df_cleaned.shape[1] - df_original.shape[1]}")

    # 数据质量改善
    original_missing = df_original.isnull().sum().sum()
    cleaned_missing = df_cleaned.isnull().sum().sum()
    print(f"\n数据质量改善:")
    print(f"原始缺失值: {original_missing}")
    print(f"清洗后缺失值: {cleaned_missing}")

    # 新增字段统计
    new_columns = set(df_cleaned.columns) - set(df_original.columns)
    if new_columns:
        print(f"\n新增字段:")
        for col in new_columns:
            non_null_count = df_cleaned[col].count()
            print(f"  - {col}: {non_null_count} 个有效值")

    # 核心字段完整性
    print(f"\n核心字段完整性:")
    core_fields = ['店铺名称', '地址_清洗', '商圈', '平均价格', '价格区间']
    for field in core_fields:
        if field in df_cleaned.columns:
            completeness = (df_cleaned[field].count() / len(df_cleaned)) * 100
            print(f"  - {field}: {completeness:.1f}%")

    return {
        'original_rows': df_original.shape[0],
        'cleaned_rows': df_cleaned.shape[0],
        'removed_rows': df_original.shape[0] - df_cleaned.shape[0],
        'new_columns': len(new_columns),
        'original_missing': original_missing,
        'cleaned_missing': cleaned_missing
    }

def save_cleaned_data(df_cleaned, filename='昆山奶茶店_清洗后数据.xlsx'):
    """保存清洗后的数据"""
    print(f"\n=== 保存清洗后数据 ===")

    # 选择要保存的核心字段
    core_columns = []

    # 基础字段
    if '店铺ID' in df_cleaned.columns:
        core_columns.append('店铺ID')
    if '店铺名称' in df_cleaned.columns:
        core_columns.append('店铺名称')
    if '地址_清洗' in df_cleaned.columns:
        core_columns.append('地址')
        df_cleaned['地址'] = df_cleaned['地址_清洗']
    if '商圈' in df_cleaned.columns:
        core_columns.append('商圈')

    # 价格字段
    if '平均价格' in df_cleaned.columns:
        core_columns.append('人均消费')
        df_cleaned['人均消费'] = df_cleaned['平均价格']
    if '价格区间' in df_cleaned.columns:
        core_columns.append('价格区间')

    # 评分字段
    if '评分' in df_cleaned.columns:
        core_columns.append('评分')
    if '评分等级' in df_cleaned.columns:
        core_columns.append('评分等级')

    # 原始字段（保留参考）
    original_fields = ['标题', '价格', '地址', '推荐']
    for field in original_fields:
        if field in df_cleaned.columns:
            core_columns.append(f'原始_{field}')
            df_cleaned[f'原始_{field}'] = df_cleaned[field]

    # 创建最终数据集
    final_df = df_cleaned[core_columns].copy()

    # 保存到Excel
    final_df.to_excel(filename, index=False)
    print(f"✓ 清洗后数据已保存到: {filename}")
    print(f"  包含字段: {', '.join(core_columns)}")
    print(f"  数据行数: {len(final_df)}")

    return final_df

def main():
    """主函数"""
    file_path = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"

    # 1. 加载和分析原始数据
    df_original = load_and_analyze_data(file_path)

    if df_original is None:
        print("❌ 数据加载失败，程序退出")
        return

    # 2. 数据清洗
    df_cleaned = clean_data(df_original)

    # 3. 创建衍生字段
    df_final = create_derived_fields(df_cleaned)

    # 4. 生成清洗报告
    report = generate_cleaning_report(df_original, df_final)

    # 5. 保存清洗后的数据
    final_df = save_cleaned_data(df_final)

    # 6. 数据预览
    print(f"\n=== 清洗后数据预览 ===")
    print("前5行数据:")
    print(final_df.head())

    # 7. 统计信息
    print(f"\n=== 统计信息 ===")
    if '商圈' in final_df.columns:
        print("商圈分布:")
        print(final_df['商圈'].value_counts())

    if '价格区间' in final_df.columns:
        print("\n价格区间分布:")
        print(final_df['价格区间'].value_counts())

    if '评分等级' in final_df.columns:
        print("\n评分等级分布:")
        print(final_df['评分等级'].value_counts())

    print(f"\n=== 数据清洗完成 ===")
    print("✓ 数据清洗成功完成！")
    print("✓ 可以开始使用FineBI进行分析")
    print("\n下一步操作:")
    print("1. 打开FineBI软件")
    print("2. 导入 '昆山奶茶店_清洗后数据.xlsx' 文件")
    print("3. 按照FineBI操作指南制作仪表盘")

if __name__ == "__main__":
    main()
