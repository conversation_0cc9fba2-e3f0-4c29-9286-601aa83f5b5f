#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本 - 分析八爪鱼爬取的大众点评奶茶店数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def analyze_excel_data(file_path):
    """分析Excel数据"""
    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(file_path)
        
        print(f"数据形状: {df.shape}")
        print(f"总共有 {df.shape[0]} 行数据，{df.shape[1]} 列")
        
        print("\n列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"{i}. {col}")
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n前5行数据:")
        print(df.head())
        
        print("\n数据基本统计信息:")
        print(df.describe(include='all'))
        
        print("\n缺失值统计:")
        missing_data = df.isnull().sum()
        print(missing_data[missing_data > 0])
        
        print("\n重复数据统计:")
        print(f"重复行数: {df.duplicated().sum()}")
        
        # 保存数据概览到文件
        with open('data_overview.txt', 'w', encoding='utf-8') as f:
            f.write(f"数据形状: {df.shape}\n")
            f.write(f"总共有 {df.shape[0]} 行数据，{df.shape[1]} 列\n\n")
            f.write("列名:\n")
            for i, col in enumerate(df.columns, 1):
                f.write(f"{i}. {col}\n")
            f.write(f"\n数据类型:\n{df.dtypes}\n")
            f.write(f"\n缺失值统计:\n{missing_data[missing_data > 0]}\n")
            f.write(f"\n重复数据统计:\n重复行数: {df.duplicated().sum()}\n")
        
        return df
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def clean_data(df):
    """数据清洗"""
    print("\n开始数据清洗...")
    
    # 删除重复数据
    df_cleaned = df.drop_duplicates()
    print(f"删除重复数据后: {df_cleaned.shape}")
    
    # 删除全为空的行
    df_cleaned = df_cleaned.dropna(how='all')
    print(f"删除全空行后: {df_cleaned.shape}")
    
    return df_cleaned

def suggest_analysis_directions(df):
    """根据数据特征建议分析方向"""
    print("\n=== 数据分析建议 ===")
    
    suggestions = []
    
    # 检查可能的分析维度
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['店名', 'name', '名称']):
            suggestions.append(f"1. 店铺分析: 基于'{col}'列分析店铺分布")
        elif any(keyword in col_lower for keyword in ['评分', 'score', 'rating', '分数']):
            suggestions.append(f"2. 评分分析: 基于'{col}'列分析评分分布和趋势")
        elif any(keyword in col_lower for keyword in ['价格', 'price', '人均', '消费']):
            suggestions.append(f"3.价格分析: 基于'{col}'列分析价格区间分布")
        elif any(keyword in col_lower for keyword in ['地址', 'address', '位置', '区域']):
            suggestions.append(f"4. 地理分析: 基于'{col}'列分析地理分布")
        elif any(keyword in col_lower for keyword in ['评论', 'comment', '点评']):
            suggestions.append(f"5. 评论分析: 基于'{col}'列分析用户反馈")
        elif any(keyword in col_lower for keyword in ['类型', 'type', '分类', 'category']):
            suggestions.append(f"6. 分类分析: 基于'{col}'列分析业务类型分布")
    
    for suggestion in suggestions:
        print(suggestion)
    
    return suggestions

if __name__ == "__main__":
    file_path = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"
    
    # 分析数据
    df = analyze_excel_data(file_path)
    
    if df is not None:
        # 数据清洗
        df_cleaned = clean_data(df)
        
        # 保存清洗后的数据
        df_cleaned.to_excel('cleaned_data.xlsx', index=False)
        print(f"\n清洗后的数据已保存到 'cleaned_data.xlsx'")
        
        # 建议分析方向
        suggest_analysis_directions(df_cleaned)
        
        print("\n=== 下一步建议 ===")
        print("1. 查看 'data_overview.txt' 了解数据详情")
        print("2. 使用 'cleaned_data.xlsx' 进行FineBI分析")
        print("3. 根据上述分析建议制作仪表盘")
