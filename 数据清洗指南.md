# 数据清洗指南 - 奶茶店数据处理

## 1. 数据检查清单

### 1.1 基本信息检查
- [ ] 数据总行数和列数
- [ ] 每列的数据类型
- [ ] 列名是否清晰明确
- [ ] 是否有空列或空行

### 1.2 数据质量检查
- [ ] 重复数据检查
- [ ] 缺失值统计
- [ ] 异常值识别
- [ ] 数据格式一致性

## 2. 常见数据问题及处理方法

### 2.1 重复数据
**识别方法：**
- 完全重复：所有字段都相同
- 部分重复：关键字段相同（如店名+地址）

**处理方法：**
- 删除完全重复的记录
- 对于部分重复，保留信息最完整的记录

### 2.2 缺失值处理
**常见缺失情况：**
- 评分缺失：可能是新店或未被评价
- 地址缺失：数据爬取不完整
- 价格缺失：店铺未公开价格信息

**处理策略：**
- 评分缺失：标记为"暂无评分"或使用平均值
- 地址缺失：尝试通过店名补充或删除记录
- 价格缺失：标记为"价格面议"或使用同类店铺均值

### 2.3 异常值处理
**可能的异常值：**
- 评分超出正常范围（如>5分或<0分）
- 价格异常（如0元或过高价格）
- 地址格式不统一

**处理方法：**
- 评分：限制在合理范围内（0-5分）
- 价格：设置合理的上下限
- 地址：标准化格式，提取关键信息

## 3. 数据标准化

### 3.1 地址信息标准化
```
原始地址：江苏省苏州市昆山市朝阳东路123号
标准化后：
- 省份：江苏省
- 城市：苏州市
- 区县：昆山市
- 详细地址：朝阳东路123号
- 商圈：朝阳商圈（需要人工标注或地理编码）
```

### 3.2 价格信息标准化
```
原始价格：人均¥25-30元
标准化后：
- 最低价格：25
- 最高价格：30
- 平均价格：27.5
- 价格区间：25-30元
```

### 3.3 评分信息标准化
```
原始评分：4.5分
标准化后：
- 数值评分：4.5
- 等级评分：优秀（4.5-5.0）
- 星级：4.5星
```

## 4. 创建衍生字段

### 4.1 价格分级
```
价格区间分类：
- 经济型：0-20元
- 中档型：21-35元
- 高档型：36-50元
- 奢华型：50元以上
```

### 4.2 评分分级
```
评分等级分类：
- 优秀：4.5-5.0分
- 良好：4.0-4.4分
- 一般：3.5-3.9分
- 较差：3.0-3.4分
- 很差：3.0分以下
```

### 4.3 商圈分类
```
根据地址信息提取商圈：
- 市中心商圈
- 大学城商圈
- 住宅区商圈
- 工业区商圈
- 其他商圈
```

## 5. 数据验证

### 5.1 逻辑验证
- 评分范围是否合理（0-5分）
- 价格是否为正数
- 地址信息是否完整
- 店名是否重复但地址不同

### 5.2 一致性验证
- 同一店铺的信息是否一致
- 数据格式是否统一
- 分类标准是否一致

## 6. 清洗后数据结构建议

### 6.1 核心字段
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| 店铺ID | 文本 | 唯一标识 | SHOP_001 |
| 店铺名称 | 文本 | 店铺名称 | 一点点奶茶 |
| 地址 | 文本 | 详细地址 | 昆山市朝阳东路123号 |
| 商圈 | 文本 | 所属商圈 | 朝阳商圈 |
| 评分 | 数值 | 用户评分 | 4.5 |
| 评分等级 | 文本 | 评分等级 | 优秀 |
| 人均消费 | 数值 | 平均价格 | 28 |
| 价格区间 | 文本 | 价格分类 | 中档型 |
| 评论数 | 数值 | 评论总数 | 156 |
| 营业状态 | 文本 | 是否营业 | 营业中 |

### 6.2 扩展字段
| 字段名 | 数据类型 | 说明 | 示例 |
|--------|----------|------|------|
| 经度 | 数值 | 地理坐标 | 120.9876 |
| 纬度 | 数值 | 地理坐标 | 31.1234 |
| 开业时间 | 日期 | 开业日期 | 2023-01-15 |
| 主营产品 | 文本 | 产品类型 | 奶茶,咖啡 |
| 特色标签 | 文本 | 店铺特色 | 网红店,24小时 |

## 7. 质量控制检查点

### 7.1 数据完整性
- 核心字段完整率 > 95%
- 关键信息无缺失
- 数据格式统一

### 7.2 数据准确性
- 评分在合理范围内
- 价格信息真实有效
- 地址信息准确

### 7.3 数据一致性
- 同类数据格式一致
- 分类标准统一
- 命名规范一致

## 8. 清洗工具推荐

### 8.1 Excel工具
- 数据透视表：快速统计分析
- 条件格式：标识异常数据
- 筛选功能：查找特定数据

### 8.2 专业工具
- OpenRefine：开源数据清洗工具
- Tableau Prep：可视化数据准备
- Python pandas：编程方式处理

### 8.3 FineBI内置功能
- 数据预处理：基本清洗功能
- 字段计算：创建衍生字段
- 数据验证：质量检查功能

## 9. 清洗完成标准

数据清洗完成应满足以下标准：
- [ ] 无重复记录
- [ ] 核心字段完整率>95%
- [ ] 数据格式统一
- [ ] 异常值已处理
- [ ] 创建必要的衍生字段
- [ ] 通过数据质量验证
- [ ] 生成数据清洗报告

完成数据清洗后，即可导入FineBI进行下一步的分析和可视化工作。
